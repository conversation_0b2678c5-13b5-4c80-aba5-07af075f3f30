import React, { useState } from 'react';

const Navbar = () => {
  const [isMoreDropdownOpen, setIsMoreDropdownOpen] = useState(false);
  const [isLearnDropdownOpen, setIsLearnDropdownOpen] = useState(false);

  return (
    <nav className="navbar">
      <div className="nav-container">
        <a href="#" className="nav-logo">
          <img src="/BullSeed_LOGO.png" alt="BullSeed" className="nav-logo-image" />
          BullSeed
        </a>

        <ul className="nav-links">
          <li><a href="#features">Features</a></li>
          <li><a href="#prices">Prices</a></li>
          <li><a href="#testimonials">Testimonials</a></li>
          <li className="nav-dropdown">
            <a
              href="#"
              className="nav-dropdown-trigger"
              onClick={(e) => {
                e.preventDefault();
                setIsMoreDropdownOpen(!isMoreDropdownOpen);
              }}
              onBlur={() => setTimeout(() => setIsMoreDropdownOpen(false), 150)}
            >
              More
              <svg
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className={`nav-dropdown-arrow ${isMoreDropdownOpen ? 'open' : ''}`}
              >
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </a>
            {isMoreDropdownOpen && (
              <div className="nav-dropdown-menu">
                <a href="/about">About Us</a>
                <a href="/nft-trade">NFT-Trade</a>
                <div
                  className="nav-dropdown-submenu"
                  onMouseEnter={() => setIsLearnDropdownOpen(true)}
                  onMouseLeave={() => setIsLearnDropdownOpen(false)}
                >
                  <a href="#" className="nav-dropdown-submenu-trigger">
                    Learn
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="nav-dropdown-arrow-right"
                    >
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </a>
                  {isLearnDropdownOpen && (
                    <div className="nav-dropdown-submenu-content">
                      <a href="/learn/plans">Plans</a>
                      <a href="/learn/faq">FAQ</a>
                    </div>
                  )}
                </div>
                <a href="/contact">Contact</a>
              </div>
            )}
          </li>
        </ul>

        <a href="#" className="nav-cta">Start Trading</a>
      </div>
    </nav>
  );
};

export default Navbar;
