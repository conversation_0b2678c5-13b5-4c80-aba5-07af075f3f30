* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0a0a0a;
  color: #ffffff;
  line-height: 1.6;
}

.App {
  min-height: 100vh;
  background-color: #0a0a0a;
}

/* Navigation Styles */
.navbar {
  position: fixed;
  top: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 0.5rem 2rem;
  width: auto;
  max-width: 800px;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3rem;
  width: 100%;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  text-decoration: none;
}

.nav-logo-image {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-links a {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  white-space: nowrap;
}

.nav-links a:hover {
  color: #ffffff;
}

.nav-cta {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  padding: 0.5rem 1.25rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  white-space: nowrap;
}

.nav-cta:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

/* Dropdown Styles */
.nav-dropdown {
  position: relative;
}

.nav-dropdown-trigger {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  vertical-align: baseline;
}

.nav-dropdown-trigger:hover {
  color: #ffffff;
}

.nav-dropdown-arrow {
  transition: transform 0.2s ease;
  width: 10px;
  height: 10px;
  flex-shrink: 0;
}

.nav-dropdown-arrow.open {
  transform: rotate(180deg);
}

.nav-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.5rem 0;
  min-width: 180px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1001;
}

.nav-dropdown-menu a {
  display: block;
  padding: 0.75rem 1rem;
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.nav-dropdown-menu a:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

.nav-dropdown-submenu {
  position: relative;
}

.nav-dropdown-submenu-trigger {
  display: block;
  padding: 0.75rem 1rem;
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-dropdown-submenu-trigger:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

.nav-dropdown-arrow-right {
  transition: transform 0.2s ease;
}

.nav-dropdown-submenu-content {
  position: absolute;
  top: 0;
  left: 100%;
  margin-left: 0.5rem;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.5rem 0;
  min-width: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1002;
}

.nav-dropdown-submenu-content a {
  display: block;
  padding: 0.75rem 1rem;
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.nav-dropdown-submenu-content a:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

/* Hero Section */
.hero {
  padding: 10rem 0 4rem;
  text-align: center;
  background: linear-gradient(180deg, #0a0a0a 0%, #111111 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(0, 212, 170, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  color: #a0a0a0;
  margin-bottom: 2rem;
}

.hero-badge-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #a0a0a0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  max-width: 600px;
  margin: 0 auto 3rem;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.btn-primary {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.btn-secondary {
  color: #ffffff;
  padding: 1rem 2rem;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.btn-secondary:hover {
  color: #00d4aa;
}

.hero-image {
  margin-top: 4rem;
  position: relative;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Trust Badges Section */
.trust-badges {
  padding: 4rem 0;
  background-color: #0a0a0a;
  border-bottom: 1px solid #1a1a1a;
  overflow: hidden;
}

.trust-badges-container {
  width: 100%;
  position: relative;
}

.trust-badges-track {
  display: flex;
  align-items: center;
  gap: 6rem;
  animation: scroll-badges 15s linear infinite;
  width: max-content;
}

@keyframes scroll-badges {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.trust-badge {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
  min-width: 200px;
  justify-content: center;
}

.trust-badge:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.trust-badge-icon {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.trust-badge-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  filter: grayscale(100%) brightness(0.6);
  transition: all 0.3s ease;
}

.trust-badge:hover .trust-badge-icon img {
  filter: grayscale(0%) brightness(1);
  transform: scale(1.1);
}

.trust-badge-name {
  color: #888;
  font-size: 1.1rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.trust-badge:hover .trust-badge-name {
  color: #ffffff;
}

/* Features Section */
.features {
  padding: 6rem 0;
  background-color: #0a0a0a;
  position: relative;
}

.features-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 3rem;
}

.features-header {
  text-align: left;
  margin-bottom: 4rem;
}

.features-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 4rem;
  align-items: flex-start;
}

.features-left {
  display: flex;
  flex-direction: column;
}

.features-title {
  font-size: 4.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.features-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.features-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  line-height: 1.6;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  color: #666;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.feature-description {
  color: #888;
  line-height: 1.4;
  font-size: 0.875rem;
}

.features-right {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-top: 1rem;
}

.dashboard-preview {
  width: 100%;
  max-width: 600px;
}

.dashboard-preview img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}


/* Testimonials Section */
.testimonials {
  padding: 6rem 0;
  background-color: #0a0a0a;
  overflow: hidden;
}

.testimonials-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 3rem;
}

.testimonials-header {
  text-align: center;
  margin-bottom: 4rem;
}

.testimonials-title {
  font-size: 4rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
}

.testimonials-subtitle {
  font-size: 1.25rem;
  color: #a0a0a0;
}

.testimonials-scroll-container {
  width: 100%;
  position: relative;
}

.testimonials-track {
  display: flex;
  gap: 3rem;
  animation: scroll-testimonials 25s linear infinite;
  width: max-content;
}

@keyframes scroll-testimonials {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.testimonial-card {
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  padding: 2.5rem;
  border-radius: 16px;
  border: 1px solid #333;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 400px;
  max-width: 400px;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.15);
  border-color: #00d4aa;
}

.testimonial-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #333;
  transition: border-color 0.3s ease;
}

.testimonial-card:hover .testimonial-avatar {
  border-color: #00d4aa;
}

.testimonial-author {
  flex: 1;
}

.testimonial-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.testimonial-role {
  font-size: 0.875rem;
  color: #00d4aa;
  font-weight: 500;
}

.testimonial-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #e0e0e0;
  font-style: normal;
}

/* CTA Section */
.cta {
  padding: 6rem 0;
  background: linear-gradient(135deg, #111111 0%, #0a0a0a 100%);
  position: relative;
  text-align: center;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  pointer-events: none;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.cta-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
}

.cta-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.btn-cta {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.125rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: inline-block;
}

.btn-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

/* Footer */
.footer {
  background-color: #111111;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 3rem 0 1rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 2rem;
}

.footer-brand {
  max-width: 300px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.footer-logo-image {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.footer-logo span {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
}

.footer-description {
  color: #a0a0a0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
  color: #a0a0a0;
}

.footer-social a:hover {
  background: rgba(0, 212, 170, 0.1);
  border-color: #00d4aa;
  color: #00d4aa;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.footer-column h4 {
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 1rem;
}

.footer-column ul {
  list-style: none;
}

.footer-column ul li {
  margin-bottom: 0.5rem;
}

.footer-column ul li a {
  color: #a0a0a0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-column ul li a:hover {
  color: #00d4aa;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
}

.footer-bottom p {
  color: #666666;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-links {
    display: none;
  }

  .nav-dropdown-menu {
    right: -1rem;
    min-width: 200px;
  }

  .nav-dropdown-submenu-content {
    position: fixed;
    top: auto;
    left: 1rem;
    right: 1rem;
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .features-title {
    font-size: 2.5rem;
  }

  .features-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .features-header {
    text-align: center;
  }

  .testimonials-title {
    font-size: 2.5rem;
  }

  .testimonial-card {
    min-width: 300px;
    max-width: 300px;
    padding: 2rem;
  }

  .testimonials-track {
    gap: 2rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .footer-links {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
