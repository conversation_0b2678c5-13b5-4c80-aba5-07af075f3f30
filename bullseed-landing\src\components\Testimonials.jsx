import React from 'react';

const Testimonials = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Early Crypto Investor',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      text: 'The customer support is exceptional, and the platform\'s intuitive design made getting started with crypto trading seamless. A game-changer for both beginners and pros.'
    },
    {
      name: '<PERSON>',
      role: 'DeFi Developer',
      avatar: 'https://images.unsplash.com/photo-*************-2616b332c1c2?w=150&h=150&fit=crop&crop=face',
      text: 'We\'ve seen remarkable improvements in our trading efficiency since switching to CryptoTrade. The smart order routing and liquidity aggregation are particularly impressive.'
    },
    {
      name: '<PERSON>',
      role: 'Crypto Security Expert',
      avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      text: 'The security features are robust and the regular updates keep us ahead of emerging threats. It\'s exactly what the crypto industry needed.'
    },
    {
      name: '<PERSON>',
      role: 'Portfolio Manager',
      avatar: 'https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      text: 'BullSeed\'s advanced analytics and risk management tools have transformed how we approach institutional trading. The API integration is seamless.'
    },
    {
      name: 'Michael Thompson',
      role: 'Quantitative Trader',
      avatar: 'https://images.unsplash.com/photo-*************-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      text: 'The algorithmic trading features and backtesting capabilities are industry-leading. Our strategies perform significantly better on this platform.'
    },
    {
      name: 'Lisa Chen',
      role: 'Crypto Fund Manager',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
      text: 'The institutional-grade infrastructure and compliance tools make BullSeed our go-to platform for managing large crypto portfolios.'
    }
  ];

  return (
    <section className="testimonials" id="testimonials">
      <div className="testimonials-container">
        <div className="testimonials-header">
          <h2 className="testimonials-title">Trusted by Traders</h2>
          <p className="testimonials-subtitle">Join thousands of satisfied traders on CryptoTrade</p>
        </div>

        <div className="testimonials-scroll-container">
          <div className="testimonials-track">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="testimonial-card">
                <div className="testimonial-header">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="testimonial-avatar"
                    onError={(e) => {
                      e.target.src = `https://ui-avatars.com/api/?name=${testimonial.name}&background=00d4aa&color=fff&size=60`;
                    }}
                  />
                  <div className="testimonial-author">
                    <h4 className="testimonial-name">{testimonial.name}</h4>
                    <p className="testimonial-role">{testimonial.role}</p>
                  </div>
                </div>
                <p className="testimonial-text">{testimonial.text}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
